# Gold Returns Analysis with Markov Chain Visualization
# Generates three key visualizations for gold price return analysis

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime

# Set style for better-looking plots
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

def generate_gold_returns_data():
    """Generate simulated monthly gold returns data for 2010-2020"""
    # Seed for reproducibility
    np.random.seed(42)
    
    # Generate monthly date range
    months = pd.date_range(start='2010-01-01', periods=132, freq='M')
    
    # Simulate monthly returns with realistic parameters
    # Gold typically has moderate volatility with slight positive bias
    returns = np.random.normal(loc=0.5, scale=2.5, size=132)
    
    return months, returns

def discretize_returns(returns):
    """Discretize returns into 21 states (S-10 to S+10)"""
    # Create state labels
    state_labels = [f"S{i:+d}" if i != 0 else "S0" for i in range(-10, 11)]
    
    # Create bin edges - 22 edges for 21 bins
    bin_edges = np.linspace(-11, 11, 22)
    
    # Discretize returns
    state_indices = pd.cut(returns, bins=bin_edges, labels=state_labels, include_lowest=True)
    
    return state_indices, state_labels

def build_transition_matrix(states, state_labels):
    """Build transition probability matrix for Markov Chain"""
    # Initialize transition count matrix
    transition_counts = pd.DataFrame(0, index=state_labels, columns=state_labels, dtype=int)
    
    # Count transitions
    for i in range(len(states) - 1):
        current_state = states[i]
        next_state = states[i + 1]
        
        if pd.notnull(current_state) and pd.notnull(next_state):
            transition_counts.loc[current_state, next_state] += 1
    
    # Convert to probabilities
    transition_probs = transition_counts.div(transition_counts.sum(axis=1), axis=0).fillna(0)
    
    return transition_probs, transition_counts

def plot_returns_scatter(months, returns, states):
    """Create scatter plot of returns with state labels"""
    # Create DataFrame for plotting
    df = pd.DataFrame({
        'Month': months,
        'Return (%)': returns,
        'State': states
    })
    
    plt.figure(figsize=(15, 8))
    
    # Create scatter plot
    scatter = plt.scatter(df['Month'], df['Return (%)'], 
                         c=pd.Categorical(df['State']).codes, 
                         cmap='Spectral', s=60, alpha=0.7, edgecolors='black', linewidth=0.5)
    
    plt.title('Monthly Gold Price Returns (2010–2020) with State Labels', 
              fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('Month', fontsize=12)
    plt.ylabel('Return (%)', fontsize=12)
    
    # Format x-axis
    plt.xticks(rotation=45)
    
    # Add colorbar
    cbar = plt.colorbar(scatter)
    cbar.set_label('State Index', fontsize=12)
    
    # Add grid
    plt.grid(True, alpha=0.3)
    
    # Add horizontal line at y=0
    plt.axhline(y=0, color='red', linestyle='--', alpha=0.5)
    
    plt.tight_layout()
    return plt.gcf()

def plot_transition_heatmap(transition_matrix):
    """Create heatmap of transition probability matrix"""
    plt.figure(figsize=(14, 12))
    
    # Create heatmap
    sns.heatmap(transition_matrix, 
                cmap='YlOrRd', 
                annot=False, 
                fmt='.3f', 
                linewidths=0.1,
                cbar_kws={'label': 'Transition Probability'})
    
    plt.title('Discrete Markov Chain Transition Probability Matrix', 
              fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('Next State', fontsize=12)
    plt.ylabel('Current State', fontsize=12)
    
    # Rotate labels for better readability
    plt.xticks(rotation=45)
    plt.yticks(rotation=0)
    
    plt.tight_layout()
    return plt.gcf()

def plot_mae_comparison():
    """Create bar plot comparing MAE values"""
    # Simulate MAE values (you can replace with actual calculated values)
    np.random.seed(42)
    mae_mc = np.round(np.mean(np.abs(np.random.normal(0.5, 0.3, 12))), 3)
    mae_mcfs = np.round(mae_mc - 0.3, 3)
    
    plt.figure(figsize=(8, 6))
    
    models = ['Discrete Markov Chain', 'Fuzzy Markov Chain']
    mae_values = [mae_mc, mae_mcfs]
    colors = ['#FF6B6B', '#4ECDC4']
    
    bars = plt.bar(models, mae_values, color=colors, alpha=0.8, edgecolor='black', linewidth=1)
    
    plt.title('Mean Absolute Error (MAE) Comparison', 
              fontsize=16, fontweight='bold', pad=20)
    plt.ylabel('MAE', fontsize=12)
    
    # Add value labels on bars
    for i, (bar, value) in enumerate(zip(bars, mae_values)):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                f'{value}', ha='center', va='bottom', fontweight='bold', fontsize=11)
    
    # Add grid
    plt.grid(True, alpha=0.3, axis='y')
    
    plt.tight_layout()
    return plt.gcf()

def main():
    """Main function to generate all visualizations"""
    print("Generating Gold Returns Analysis Visualizations...")
    
    # Generate data
    months, returns = generate_gold_returns_data()
    states, state_labels = discretize_returns(returns)
    transition_matrix, transition_counts = build_transition_matrix(states, state_labels)
    
    # Create visualizations
    print("Creating scatter plot...")
    fig1 = plot_returns_scatter(months, returns, states)
    
    print("Creating transition matrix heatmap...")
    fig2 = plot_transition_heatmap(transition_matrix)
    
    print("Creating MAE comparison plot...")
    fig3 = plot_mae_comparison()
    
    # Show all plots
    plt.show()
    
    # Print some statistics
    print(f"\nData Summary:")
    print(f"Total months analyzed: {len(returns)}")
    print(f"Average return: {np.mean(returns):.2f}%")
    print(f"Return volatility: {np.std(returns):.2f}%")
    print(f"Number of states: {len(state_labels)}")
    print(f"Non-zero transitions: {(transition_counts > 0).sum().sum()}")

if __name__ == "__main__":
    main()
