#!/usr/bin/env python3
"""
Simple runner script for the gold returns analysis.
This script will check dependencies and run the main analysis.
"""

import sys
import subprocess
import importlib

def check_and_install_dependencies():
    """Check if required packages are installed, install if missing"""
    required_packages = ['pandas', 'numpy', 'matplotlib', 'seaborn']
    missing_packages = []
    
    for package in required_packages:
        try:
            importlib.import_module(package)
            print(f"✓ {package} is installed")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package} is missing")
    
    if missing_packages:
        print(f"\nInstalling missing packages: {', '.join(missing_packages)}")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + missing_packages)
            print("✓ All packages installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"✗ Failed to install packages: {e}")
            return False
    
    return True

def main():
    """Main runner function"""
    print("Gold Returns Analysis Runner")
    print("=" * 40)
    
    # Check dependencies
    if not check_and_install_dependencies():
        print("Failed to install required dependencies. Exiting.")
        return
    
    print("\nRunning analysis...")
    try:
        # Import and run the main analysis
        from gold_returns_analysis import main as run_analysis
        run_analysis()
        print("\n✓ Analysis completed successfully!")
    except Exception as e:
        print(f"✗ Error running analysis: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
